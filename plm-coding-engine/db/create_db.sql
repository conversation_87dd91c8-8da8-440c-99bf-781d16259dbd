/*
 Navicat Premium Dump SQL

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 80042 (8.0.42)
 Source Host           : localhost:3306
 Source Schema         : plm-coding-engine

 Target Server Type    : MySQL
 Target Server Version : 80042 (8.0.42)
 File Encoding         : 65001

 Date: 07/07/2025 11:04:21
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for enovia_classification_attr
-- ----------------------------
DROP TABLE IF EXISTS `enovia_classification_attr`;
CREATE TABLE `enovia_classification_attr`  (
  `ID` int NOT NULL AUTO_INCREMENT,
  `CLASSIFICATION_ID` int NOT NULL,
  `ATTRIBUTE_NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `ATTRIBUTE_NAME_CN` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '中文属性名称',
  `ATTRIBUTE_NAME_EN` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '英文属性名称',
  `ATTRIBUTE_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `DATA_TYPE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `IS_REQUIRED` enum('Y','N') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'N',
  `DESCRIPTION` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `DESCRIPTION_CN` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '中文描述',
  `DESCRIPTION_EN` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '英文描述',
  `CREATED_AT` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATED_AT` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `DEFAULT_VALUE` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '默认值',
  `ENOVIA_ATTRIBUTE_NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'Enovia中的属性名称',
  `SYNC_STATUS` enum('PENDING','SYNCING','SUCCESS','FAILED') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'PENDING' COMMENT '同步状态',
  `LAST_SYNC_TIME` timestamp NULL DEFAULT NULL COMMENT '最后同步时间',
  `DYNAMIC_ATTRIBUTES` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '动态属性JSON',
  `EXT_ATTR_1` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '扩展属性1',
  `EXT_ATTR_2` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '扩展属性2',
  `EXT_ATTR_3` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '扩展属性3',
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `UQ_ENOVIA_CLASSIFICATION_ATTR`(`CLASSIFICATION_ID` ASC, `ATTRIBUTE_CODE` ASC) USING BTREE,
  CONSTRAINT `FK_ENOVIA_CLASSIFICATION_ATTR` FOREIGN KEY (`CLASSIFICATION_ID`) REFERENCES `MATERIAL_CLASSIFICATION` (`ID`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'Enovia分类属性定义表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for coding_rule
-- ----------------------------
DROP TABLE IF EXISTS `coding_rule`;
CREATE TABLE `coding_rule`  (
  `ID` bigint NOT NULL AUTO_INCREMENT,
  `RULE_NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '规则名称',
  `DESCRIPTION` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '规则描述',
  `OBJECT_TYPE_CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '对象类型编码',
  `PRIORITY` int NOT NULL COMMENT '优先级',
  `ENABLE_CHECK_DIGIT` enum('Y','N') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'N' COMMENT '是否启用校验位',
  `IS_ACTIVE` enum('Y','N') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'Y' COMMENT '是否激活',
  `CREATED_BY` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `LAST_MODIFIED_BY` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '最后修改人',
  `LAST_MODIFIED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `UK_RULE_NAME`(`RULE_NAME` ASC) USING BTREE,
  UNIQUE INDEX `UK_OBJECT_TYPE_PRIORITY`(`OBJECT_TYPE_CODE` ASC, `PRIORITY` ASC) USING BTREE,
  UNIQUE INDEX `UKrilxu0ejdnhx8i7r09nejwy4t`(`OBJECT_TYPE_CODE` ASC, `PRIORITY` ASC) USING BTREE,
  UNIQUE INDEX `UK_rc6g24096vusggp038tkasm2v`(`RULE_NAME` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '编码规则表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for coding_rule_segment
-- ----------------------------
DROP TABLE IF EXISTS `coding_rule_segment`;
CREATE TABLE `coding_rule_segment`  (
  `ID` bigint NOT NULL AUTO_INCREMENT,
  `RULE_ID` bigint NOT NULL COMMENT '规则ID',
  `SEGMENT_ORDER` int NOT NULL COMMENT '段序号',
  `SEGMENT_TYPE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '段类型',
  `FIXED_VALUE` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '固定值',
  `TIME_FORMAT` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '时间格式',
  `ATTRIBUTE_CODE` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '属性编码',
  `CLASSIFICATION_CODE` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分类编码',
  `SEQUENCE_NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '序列名称',
  `LENGTH` int NOT NULL COMMENT '长度',
  `PADDING_CHAR` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '填充字符',
  `PADDING_DIRECTION` enum('LEFT','RIGHT') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'LEFT' COMMENT '填充方向',
  `TRUNCATE_DIRECTION` enum('LEFT','RIGHT') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'LEFT' COMMENT '截断方向',
  `DESCRIPTION` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `CREATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `PREFIX` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '前缀',
  `SUFFIX` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '后缀',
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `UK_RULE_SEGMENT_ORDER`(`RULE_ID` ASC, `SEGMENT_ORDER` ASC) USING BTREE,
  UNIQUE INDEX `UK68os2np8momchu9sswv1q7o7p`(`RULE_ID` ASC, `SEGMENT_ORDER` ASC) USING BTREE,
  CONSTRAINT `coding_rule_segment_ibfk_1` FOREIGN KEY (`RULE_ID`) REFERENCES `CODING_RULE` (`ID`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `FKocftqmp8wlw8l9361lq3hry03` FOREIGN KEY (`RULE_ID`) REFERENCES `coding_rule` (`ID`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 35 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '编码规则段表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for generated_material_codes
-- ----------------------------
DROP TABLE IF EXISTS `generated_material_codes`;
CREATE TABLE `generated_material_codes`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `generated_at` datetime(6) NULL DEFAULT NULL,
  `generated_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `generated_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `input_attributes_snapshot` json NULL,
  `material_master_id` bigint NULL DEFAULT NULL,
  `rule_version_used` int NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `UK_edlxbyfj33gofydyg8e7mxgar`(`generated_code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for material_classification
-- ----------------------------
DROP TABLE IF EXISTS `material_classification`;
CREATE TABLE `material_classification`  (
  `ID` int NOT NULL AUTO_INCREMENT,
  `NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `NAME_CN` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '中文名称',
  `NAME_EN` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '英文名称',
  `CODE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `PARENT_ID` int NULL DEFAULT NULL,
  `DESCRIPTION` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `DESCRIPTION_CN` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '中文描述',
  `DESCRIPTION_EN` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '英文描述',
  `CREATED_AT` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATED_AT` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `ENOVIA_ID` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'Enovia中的分类ID',
  `SYNC_STATUS` enum('PENDING','SYNCING','SUCCESS','FAILED') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'PENDING' COMMENT '同步状态',
  `LAST_SYNC_TIME` timestamp NULL DEFAULT NULL COMMENT '最后同步时间',
  `DYNAMIC_ATTRIBUTES` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '动态属性JSON',
  `EXT_ATTR_1` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '扩展属性1',
  `EXT_ATTR_2` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '扩展属性2',
  `EXT_ATTR_3` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '扩展属性3',
  `EXT_ATTR_4` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '扩展属性4',
  `EXT_ATTR_5` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '扩展属性5',
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `CODE`(`CODE` ASC) USING BTREE,
  UNIQUE INDEX `UK_4121llnm3j8hv3cck2ehg0y9m`(`CODE` ASC) USING BTREE,
  INDEX `FK_PARENT_CLASSIFICATION`(`PARENT_ID` ASC) USING BTREE,
  CONSTRAINT `FK_PARENT_CLASSIFICATION` FOREIGN KEY (`PARENT_ID`) REFERENCES `MATERIAL_CLASSIFICATION` (`ID`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '物料分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for material_master
-- ----------------------------
DROP TABLE IF EXISTS `material_master`;
CREATE TABLE `material_master`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `classification_attributes` json NULL,
  `classification_id` bigint NOT NULL,
  `created_at` datetime(6) NULL DEFAULT NULL,
  `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `general_attributes` json NULL,
  `model_attributes` json NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `part_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `updated_at` datetime(6) NULL DEFAULT NULL,
  `updated_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `UK_exlaopahq62w2kwbfblsspjnh`(`part_number` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sequence_config
-- ----------------------------
DROP TABLE IF EXISTS `sequence_config`;
CREATE TABLE `sequence_config`  (
  `SEQUENCE_NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '序列名称',
  `CURRENT_VALUE` bigint NOT NULL DEFAULT 1 COMMENT '当前值',
  `PREFIX` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '前缀',
  `SUFFIX` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '后缀',
  `INCREMENT_BY` int NOT NULL DEFAULT 1 COMMENT '增量值',
  `UPDATED_AT` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `INITIAL_VALUE` bigint NULL DEFAULT NULL COMMENT '初始值',
  PRIMARY KEY (`SEQUENCE_NAME`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '序列配置表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
