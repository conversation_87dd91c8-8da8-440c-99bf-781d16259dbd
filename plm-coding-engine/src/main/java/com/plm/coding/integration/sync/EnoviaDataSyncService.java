package com.plm.coding.integration.sync;

import com.plm.coding.integration.EnoviaConnectionService;
import com.plm.coding.model.*;
import com.plm.coding.repository.*;
import lombok.extern.slf4j.Slf4j;
import matrix.db.Context;
import matrix.db.MQLCommand;
import matrix.util.MatrixException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Enovia 数据同步服务
 * 负责从 Enovia 系统同步类型、分类、属性等数据到本地数据库
 */
@Slf4j
@Service
@Transactional
public class EnoviaDataSyncService {
    
    @Autowired
    private EnoviaConnectionService enoviaConnectionService;
    
    @Autowired
    private EnoviaSyncProperties syncProperties;
    
    @Autowired
    private EnoviaTypeRepository enoviaTypeRepository;
    
    @Autowired
    private EnoviaTypeAttributeRepository enoviaTypeAttributeRepository;
    
    @Autowired
    private MaterialClassificationRepository classificationRepository;
    
    @Autowired
    private ClassificationAttrDefRepository classificationAttrDefRepository;
    
    @Autowired
    private EnoviaSyncRecordRepository syncRecordRepository;
    
    /**
     * 执行全量同步
     */
    public CompletableFuture<EnoviaSyncRecord> performFullSync() {
        return CompletableFuture.supplyAsync(() -> {
            log.info("开始执行全量数据同步");
            EnoviaSyncRecord record = createSyncRecord(
                EnoviaSyncRecord.SyncType.ALL, 
                EnoviaSyncRecord.SyncStrategy.FULL
            );
            
            try {
                record.setSyncStatus(EnoviaSyncRecord.SyncStatus.RUNNING);
                syncRecordRepository.save(record);
                
                // 同步类型
                if (syncProperties.getTypes().isEnabled()) {
                    syncTypes(record);
                }
                
                // 同步分类
                if (syncProperties.getClassifications().isEnabled()) {
                    syncClassifications(record);
                }
                
                // 同步属性
                if (syncProperties.getAttributes().isEnabled()) {
                    syncAttributes(record);
                }
                
                record.setSyncStatus(EnoviaSyncRecord.SyncStatus.SUCCESS);
                record.setEndTime(LocalDateTime.now());
                log.info("全量数据同步完成，总记录数: {}, 成功: {}, 失败: {}", 
                    record.getTotalRecords(), record.getSuccessRecords(), record.getFailedRecords());
                
            } catch (Exception e) {
                log.error("全量数据同步失败", e);
                record.setSyncStatus(EnoviaSyncRecord.SyncStatus.FAILED);
                record.setErrorMessage(e.getMessage());
                record.setEndTime(LocalDateTime.now());
            }
            
            return syncRecordRepository.save(record);
        });
    }
    
    /**
     * 执行增量同步
     */
    public CompletableFuture<EnoviaSyncRecord> performIncrementalSync() {
        return CompletableFuture.supplyAsync(() -> {
            log.info("开始执行增量数据同步");
            EnoviaSyncRecord record = createSyncRecord(
                EnoviaSyncRecord.SyncType.ALL, 
                EnoviaSyncRecord.SyncStrategy.INCREMENTAL
            );
            
            try {
                record.setSyncStatus(EnoviaSyncRecord.SyncStatus.RUNNING);
                syncRecordRepository.save(record);
                
                LocalDateTime lastSyncTime = getLastSuccessfulSyncTime();
                
                // 增量同步逻辑
                if (syncProperties.getTypes().isEnabled()) {
                    syncTypesIncremental(record, lastSyncTime);
                }
                
                if (syncProperties.getClassifications().isEnabled()) {
                    syncClassificationsIncremental(record, lastSyncTime);
                }
                
                if (syncProperties.getAttributes().isEnabled()) {
                    syncAttributesIncremental(record, lastSyncTime);
                }
                
                record.setSyncStatus(EnoviaSyncRecord.SyncStatus.SUCCESS);
                record.setEndTime(LocalDateTime.now());
                log.info("增量数据同步完成，总记录数: {}, 成功: {}, 失败: {}", 
                    record.getTotalRecords(), record.getSuccessRecords(), record.getFailedRecords());
                
            } catch (Exception e) {
                log.error("增量数据同步失败", e);
                record.setSyncStatus(EnoviaSyncRecord.SyncStatus.FAILED);
                record.setErrorMessage(e.getMessage());
                record.setEndTime(LocalDateTime.now());
            }
            
            return syncRecordRepository.save(record);
        });
    }
    
    /**
     * 同步类型数据
     */
    private void syncTypes(EnoviaSyncRecord record) throws MatrixException {
        log.info("开始同步 Enovia 类型数据");
        Context context = enoviaConnectionService.getContext();
        MQLCommand mql = new MQLCommand();
        
        // 查询所有类型
        String mqlQuery = "list type * select name parent abstract hidden description dump |";
        mql.executeCommand(context, mqlQuery);
        String result = mql.getResult();
        
        if (result != null && !result.trim().isEmpty()) {
            String[] lines = result.split("\n");
            AtomicInteger totalCount = new AtomicInteger(0);
            AtomicInteger successCount = new AtomicInteger(0);
            AtomicInteger failedCount = new AtomicInteger(0);
            
            for (String line : lines) {
                if (line.trim().isEmpty()) continue;
                
                try {
                    totalCount.incrementAndGet();
                    processTypeData(line);
                    successCount.incrementAndGet();
                } catch (Exception e) {
                    log.error("处理类型数据失败: {}", line, e);
                    failedCount.incrementAndGet();
                }
            }
            
            record.setTotalRecords(record.getTotalRecords() + totalCount.get());
            record.setSuccessRecords(record.getSuccessRecords() + successCount.get());
            record.setFailedRecords(record.getFailedRecords() + failedCount.get());
        }
        
        log.info("类型数据同步完成");
    }
    
    /**
     * 处理单个类型数据
     */
    private void processTypeData(String typeData) {
        String[] parts = typeData.split("\\|");
        if (parts.length < 5) return;
        
        String typeName = parts[0].trim();
        String parentType = parts[1].trim();
        boolean isAbstract = "TRUE".equalsIgnoreCase(parts[2].trim());
        boolean isHidden = "TRUE".equalsIgnoreCase(parts[3].trim());
        String description = parts[4].trim();
        
        // 检查是否需要同步此类型
        if (!shouldSyncType(typeName)) {
            return;
        }
        
        EnoviaType enoviaType = enoviaTypeRepository.findByTypeName(typeName)
            .orElse(new EnoviaType());
        
        enoviaType.setTypeName(typeName);
        enoviaType.setDisplayName(typeName);
        enoviaType.setParentType(parentType.isEmpty() ? null : parentType);
        enoviaType.setIsAbstract(isAbstract);
        enoviaType.setIsHidden(isHidden);
        enoviaType.setDescription(description);
        enoviaType.setSyncStatus(EnoviaType.SyncStatus.SUCCESS);
        enoviaType.setLastSyncTime(LocalDateTime.now());
        
        enoviaTypeRepository.save(enoviaType);
        log.debug("已同步类型: {}", typeName);
    }
    
    /**
     * 检查是否应该同步指定类型
     */
    private boolean shouldSyncType(String typeName) {
        List<String> includeList = syncProperties.getTypes().getInclude();
        List<String> excludeList = syncProperties.getTypes().getExclude();
        
        // 如果有包含列表，只同步列表中的类型
        if (includeList != null && !includeList.isEmpty()) {
            return includeList.contains(typeName);
        }
        
        // 如果有排除列表，排除列表中的类型
        if (excludeList != null && !excludeList.isEmpty()) {
            return !excludeList.contains(typeName);
        }
        
        return true;
    }
    
    /**
     * 创建同步记录
     */
    private EnoviaSyncRecord createSyncRecord(EnoviaSyncRecord.SyncType syncType, 
                                            EnoviaSyncRecord.SyncStrategy syncStrategy) {
        EnoviaSyncRecord record = new EnoviaSyncRecord();
        record.setSyncType(syncType);
        record.setSyncStrategy(syncStrategy);
        record.setSyncStatus(EnoviaSyncRecord.SyncStatus.PENDING);
        record.setStartTime(LocalDateTime.now());
        record.setTotalRecords(0);
        record.setSuccessRecords(0);
        record.setFailedRecords(0);
        record.setSkippedRecords(0);
        return record;
    }
    
    /**
     * 获取最后一次成功同步的时间
     */
    private LocalDateTime getLastSuccessfulSyncTime() {
        return syncRecordRepository.findLastSuccessfulSync(EnoviaSyncRecord.SyncType.ALL)
            .map(EnoviaSyncRecord::getEndTime)
            .orElse(LocalDateTime.now().minusDays(1)); // 默认同步最近一天的数据
    }
    
    /**
     * 同步分类数据
     */
    private void syncClassifications(EnoviaSyncRecord record) throws MatrixException {
        log.info("开始同步 Enovia 分类数据");
        Context context = enoviaConnectionService.getContext();
        MQLCommand mql = new MQLCommand();

        // 查询所有分类（这里假设使用 Classification 类型）
        String mqlQuery = "list businessobject Classification * select name description dump |";
        mql.executeCommand(context, mqlQuery);
        String result = mql.getResult();

        if (result != null && !result.trim().isEmpty()) {
            String[] lines = result.split("\n");
            AtomicInteger totalCount = new AtomicInteger(0);
            AtomicInteger successCount = new AtomicInteger(0);
            AtomicInteger failedCount = new AtomicInteger(0);

            for (String line : lines) {
                if (line.trim().isEmpty()) continue;

                try {
                    totalCount.incrementAndGet();
                    processClassificationData(line);
                    successCount.incrementAndGet();
                } catch (Exception e) {
                    log.error("处理分类数据失败: {}", line, e);
                    failedCount.incrementAndGet();
                }
            }

            record.setTotalRecords(record.getTotalRecords() + totalCount.get());
            record.setSuccessRecords(record.getSuccessRecords() + successCount.get());
            record.setFailedRecords(record.getFailedRecords() + failedCount.get());
        }

        log.info("分类数据同步完成");
    }

    /**
     * 处理单个分类数据
     */
    private void processClassificationData(String classificationData) {
        String[] parts = classificationData.split("\\|");
        if (parts.length < 2) return;

        String classificationName = parts[0].trim();
        String description = parts.length > 1 ? parts[1].trim() : "";

        // 检查是否需要同步此分类
        if (!shouldSyncClassification(classificationName)) {
            return;
        }

        MaterialClassification classification = classificationRepository.findByCode(classificationName)
            .orElse(new MaterialClassification());

        classification.setName(classificationName);
        classification.setCode(classificationName);
        classification.setDescription(description);
        classification.setEnoviaId(classificationName);
        classification.setSyncStatus(MaterialClassification.SyncStatus.SUCCESS);
        classification.setLastSyncTime(LocalDateTime.now());

        classificationRepository.save(classification);
        log.debug("已同步分类: {}", classificationName);
    }

    /**
     * 同步属性数据
     */
    private void syncAttributes(EnoviaSyncRecord record) throws MatrixException {
        log.info("开始同步 Enovia 属性数据");
        Context context = enoviaConnectionService.getContext();
        MQLCommand mql = new MQLCommand();

        // 查询所有属性
        String mqlQuery = "list attribute * select name type description dump |";
        mql.executeCommand(context, mqlQuery);
        String result = mql.getResult();

        if (result != null && !result.trim().isEmpty()) {
            String[] lines = result.split("\n");
            AtomicInteger totalCount = new AtomicInteger(0);
            AtomicInteger successCount = new AtomicInteger(0);
            AtomicInteger failedCount = new AtomicInteger(0);

            for (String line : lines) {
                if (line.trim().isEmpty()) continue;

                try {
                    totalCount.incrementAndGet();
                    processAttributeData(line);
                    successCount.incrementAndGet();
                } catch (Exception e) {
                    log.error("处理属性数据失败: {}", line, e);
                    failedCount.incrementAndGet();
                }
            }

            record.setTotalRecords(record.getTotalRecords() + totalCount.get());
            record.setSuccessRecords(record.getSuccessRecords() + successCount.get());
            record.setFailedRecords(record.getFailedRecords() + failedCount.get());
        }

        log.info("属性数据同步完成");
    }

    /**
     * 处理单个属性数据
     */
    private void processAttributeData(String attributeData) {
        String[] parts = attributeData.split("\\|");
        if (parts.length < 3) return;

        String attributeName = parts[0].trim();
        String attributeType = parts[1].trim();
        String description = parts[2].trim();

        // 检查是否需要同步此属性
        if (!shouldSyncAttribute(attributeName)) {
            return;
        }

        // 这里可以根据需要将属性关联到特定的分类
        // 为简化，我们将属性添加到默认分类中
        MaterialClassification defaultClassification = classificationRepository.findByCode("DEFAULT")
            .orElseGet(() -> {
                MaterialClassification newClassification = new MaterialClassification();
                newClassification.setName("默认分类");
                newClassification.setCode("DEFAULT");
                newClassification.setDescription("从Enovia同步的默认分类");
                return classificationRepository.save(newClassification);
            });

        ClassificationAttrDef attrDef = classificationAttrDefRepository
            .findByClassificationIdAndAttributeCode(defaultClassification.getId(), attributeName)
            .orElse(new ClassificationAttrDef());

        attrDef.setClassificationId(defaultClassification.getId());
        attrDef.setAttributeName(attributeName);
        attrDef.setAttributeCode(attributeName);
        attrDef.setDataType(mapEnoviaTypeToDataType(attributeType));
        attrDef.setDescription(description);
        attrDef.setEnoviaAttributeName(attributeName);
        // 设置默认值，避免数据库约束错误
        attrDef.setIsRequired("N"); // 默认设置为非必填
        attrDef.setSyncStatus(ClassificationAttrDef.SyncStatus.SUCCESS);
        attrDef.setLastSyncTime(LocalDateTime.now());

        classificationAttrDefRepository.save(attrDef);
        log.debug("已同步属性: {}", attributeName);
    }

    /**
     * 增量同步类型数据
     */
    private void syncTypesIncremental(EnoviaSyncRecord record, LocalDateTime lastSyncTime) throws MatrixException {
        log.info("开始增量同步类型数据，最后同步时间: {}", lastSyncTime);
        // 增量同步逻辑与全量同步类似，但可以添加时间过滤条件
        syncTypes(record);
    }

    /**
     * 增量同步分类数据
     */
    private void syncClassificationsIncremental(EnoviaSyncRecord record, LocalDateTime lastSyncTime) throws MatrixException {
        log.info("开始增量同步分类数据，最后同步时间: {}", lastSyncTime);
        syncClassifications(record);
    }

    /**
     * 增量同步属性数据
     */
    private void syncAttributesIncremental(EnoviaSyncRecord record, LocalDateTime lastSyncTime) throws MatrixException {
        log.info("开始增量同步属性数据，最后同步时间: {}", lastSyncTime);
        syncAttributes(record);
    }

    /**
     * 检查是否应该同步指定分类
     */
    private boolean shouldSyncClassification(String classificationName) {
        List<String> includeList = syncProperties.getClassifications().getInclude();
        List<String> excludeList = syncProperties.getClassifications().getExclude();

        if (includeList != null && !includeList.isEmpty()) {
            return includeList.contains(classificationName);
        }

        if (excludeList != null && !excludeList.isEmpty()) {
            return !excludeList.contains(classificationName);
        }

        return true;
    }

    /**
     * 检查是否应该同步指定属性
     */
    private boolean shouldSyncAttribute(String attributeName) {
        List<String> includeList = syncProperties.getAttributes().getInclude();
        List<String> excludeList = syncProperties.getAttributes().getExclude();

        if (includeList != null && !includeList.isEmpty()) {
            return includeList.contains(attributeName);
        }

        if (excludeList != null && !excludeList.isEmpty()) {
            return !excludeList.contains(attributeName);
        }

        return true;
    }

    /**
     * 将 Enovia 属性类型映射到本地数据类型
     */
    private String mapEnoviaTypeToDataType(String enoviaType) {
        switch (enoviaType.toLowerCase()) {
            case "string":
            case "text":
                return "STRING";
            case "integer":
            case "int":
                return "NUMBER";
            case "real":
            case "double":
            case "float":
                return "NUMBER";
            case "boolean":
                return "BOOLEAN";
            case "date":
            case "datetime":
                return "DATE";
            default:
                return "STRING";
        }
    }
    
    /**
     * 清除缓存
     */
    @CacheEvict(value = {"codingRules", "classifications"}, allEntries = true)
    public void clearCache() {
        log.info("已清除相关缓存");
    }
}
