// ClassificationAttrDef.java (No change)
package com.plm.coding.model;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "CLASSIFICATION_ATTR_DEF", uniqueConstraints = @UniqueConstraint(columnNames = {"CLASSIFICATION_ID", "ATTRIBUTE_CODE"}))
@Data
@NoArgsConstructor
public class EnvoiaClassificationAttr {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "CLASSIFICATION_ID", nullable = false)
    private Long classificationId;

    @Column(name = "ATTRIBUTE_NAME", nullable = false)
    private String attributeName;

    @Column(name = "ATTRIBUTE_CODE", nullable = false)
    private String attributeCode;

    @Column(name = "DATA_TYPE", nullable = false)
    private String dataType; // e.g., 'STRING', 'NUMBER', 'BOOLEAN', 'ENUM'

    @Column(name = "IS_REQUIRED", nullable = false, columnDefinition = "ENUM('Y', 'N')")
    private String isRequired; // 'Y' or 'N'

    @Column(name = "DEFAULT_VALUE")
    private String defaultValue;

    @Column(name = "DESCRIPTION")
    private String description;

    @Column(name = "CREATED_AT", updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "UPDATED_AT")
    private LocalDateTime updatedAt;

    /**
     * Enovia 中的属性名称
     */
    @Column(name = "ENOVIA_ATTRIBUTE_NAME")
    private String enoviaAttributeName;

    /**
     * 同步状态
     */
    @Column(name = "SYNC_STATUS")
    @Enumerated(EnumType.STRING)
    private SyncStatus syncStatus = SyncStatus.PENDING;

    /**
     * 最后同步时间
     */
    @Column(name = "LAST_SYNC_TIME")
    private LocalDateTime lastSyncTime;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    /**
     * 同步状态枚举
     */
    public enum SyncStatus {
        PENDING,    // 待同步
        SYNCING,    // 同步中
        SUCCESS,    // 同步成功
        FAILED      // 同步失败
    }
}