// ClassificationAttrDef.java (No change)
package com.plm.coding.model;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "ENOVIA_CLASSIFICATION_ATTR", uniqueConstraints = @UniqueConstraint(columnNames = {"CLASSIFICATION_ID", "ATTRIBUTE_CODE"}))
@Data
@NoArgsConstructor
public class EnvoiaClassificationAttr {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "CLASSIFICATION_ID", nullable = false)
    private Long classificationId;

    @Column(name = "ATTRIBUTE_NAME", nullable = false)
    private String attributeName;

    /**
     * 中文属性名称
     */
    @Column(name = "ATTRIBUTE_NAME_CN")
    private String attributeNameCn;

    /**
     * 英文属性名称
     */
    @Column(name = "ATTRIBUTE_NAME_EN")
    private String attributeNameEn;

    @Column(name = "ATTRIBUTE_CODE", nullable = false)
    private String attributeCode;

    @Column(name = "DATA_TYPE", nullable = false)
    private String dataType; // e.g., 'STRING', 'NUMBER', 'BOOLEAN', 'ENUM'

    @Column(name = "IS_REQUIRED", nullable = false, columnDefinition = "ENUM('Y', 'N')")
    private String isRequired; // 'Y' or 'N'

    @Column(name = "DEFAULT_VALUE")
    private String defaultValue;

    @Column(name = "DESCRIPTION")
    private String description;

    /**
     * 中文描述
     */
    @Column(name = "DESCRIPTION_CN", columnDefinition = "TEXT")
    private String descriptionCn;

    /**
     * 英文描述
     */
    @Column(name = "DESCRIPTION_EN", columnDefinition = "TEXT")
    private String descriptionEn;

    /**
     * 动态属性（JSON格式存储）
     */
    @Column(name = "DYNAMIC_ATTRIBUTES", columnDefinition = "TEXT")
    private String dynamicAttributes;

    /**
     * 扩展属性1
     */
    @Column(name = "EXT_ATTR_1")
    private String extAttr1;

    /**
     * 扩展属性2
     */
    @Column(name = "EXT_ATTR_2")
    private String extAttr2;

    /**
     * 扩展属性3
     */
    @Column(name = "EXT_ATTR_3")
    private String extAttr3;

    @Column(name = "CREATED_AT", updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "UPDATED_AT")
    private LocalDateTime updatedAt;

    /**
     * Enovia 中的属性名称
     */
    @Column(name = "ENOVIA_ATTRIBUTE_NAME")
    private String enoviaAttributeName;

    /**
     * 同步状态
     */
    @Column(name = "SYNC_STATUS")
    @Enumerated(EnumType.STRING)
    private SyncStatus syncStatus = SyncStatus.PENDING;

    /**
     * 最后同步时间
     */
    @Column(name = "LAST_SYNC_TIME")
    private LocalDateTime lastSyncTime;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    /**
     * 同步状态枚举
     */
    public enum SyncStatus {
        PENDING,    // 待同步
        SYNCING,    // 同步中
        SUCCESS,    // 同步成功
        FAILED      // 同步失败
    }
}