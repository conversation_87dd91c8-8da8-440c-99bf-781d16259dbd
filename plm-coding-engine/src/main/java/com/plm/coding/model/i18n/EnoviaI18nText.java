package com.plm.coding.model.i18n;

import lombok.Data;
import lombok.NoArgsConstructor;
import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * Enovia 国际化文本表
 * 用于存储所有Enovia实体的多语言文本
 */
@Entity
@Table(name = "ENOVIA_I18N_TEXT", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"ENTITY_TYPE", "ENTITY_ID", "FIELD_NAME", "LANGUAGE_CODE"}))
@Data
@NoArgsConstructor
public class EnoviaI18nText {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 实体类型（如：EnoviaType, EnoviaClassification等）
     */
    @Column(name = "ENTITY_TYPE", nullable = false)
    private String entityType;
    
    /**
     * 实体ID
     */
    @Column(name = "ENTITY_ID", nullable = false)
    private Long entityId;
    
    /**
     * 字段名称（如：displayName, description等）
     */
    @Column(name = "FIELD_NAME", nullable = false)
    private String fieldName;
    
    /**
     * 语言代码（如：zh_CN, en_US, ja_JP等）
     */
    @Column(name = "LANGUAGE_CODE", nullable = false)
    private String languageCode;
    
    /**
     * 国际化文本内容
     */
    @Column(name = "TEXT_VALUE", columnDefinition = "TEXT")
    private String textValue;
    
    /**
     * 是否为默认语言
     */
    @Column(name = "IS_DEFAULT")
    private Boolean isDefault = false;
    
    /**
     * 创建时间
     */
    @Column(name = "CREATED_AT", updatable = false)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @Column(name = "UPDATED_AT")
    private LocalDateTime updatedAt;
    
    /**
     * 同步状态
     */
    @Column(name = "SYNC_STATUS")
    @Enumerated(EnumType.STRING)
    private SyncStatus syncStatus = SyncStatus.PENDING;
    
    /**
     * 最后同步时间
     */
    @Column(name = "LAST_SYNC_TIME")
    private LocalDateTime lastSyncTime;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    /**
     * 同步状态枚举
     */
    public enum SyncStatus {
        PENDING,    // 待同步
        SYNCING,    // 同步中
        SUCCESS,    // 同步成功
        FAILED      // 同步失败
    }
}
