// MaterialClassification.java (No change)
package com.plm.coding.model;

import com.fasterxml.jackson.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "MATERIAL_CLASSIFICATION")
@Data
@NoArgsConstructor
@JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "id")
@NamedEntityGraph(
    name = "MaterialClassification.withAttributes",
    attributeNodes = @NamedAttributeNode("attributes")
)
public class MaterialClassification {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "NAME", nullable = false)
    private String name;

    @Column(name = "CODE", unique = true, nullable = false)
    private String code;

    @Column(name = "PARENT_ID")
    private Long parentId;

    @Column(name = "DESCRIPTION")
    private String description;

    @Column(name = "CREATED_AT", updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "UPDATED_AT")
    private LocalDateTime updatedAt;

    /**
     * Enovia 中的分类 ID
     */
    @Column(name = "ENOVIA_ID")
    private String enoviaId;

    /**
     * 同步状态
     */
    @Column(name = "SYNC_STATUS")
    @Enumerated(EnumType.STRING)
    private SyncStatus syncStatus = SyncStatus.PENDING;

    /**
     * 最后同步时间
     */
    @Column(name = "LAST_SYNC_TIME")
    private LocalDateTime lastSyncTime;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    @OneToMany(mappedBy = "classificationId", fetch = FetchType.EAGER)
    private List<EnvoiaClassificationAttr> attributes;

    /**
     * 同步状态枚举
     */
    public enum SyncStatus {
        PENDING,    // 待同步
        SYNCING,    // 同步中
        SUCCESS,    // 同步成功
        FAILED      // 同步失败
    }
}