package com.plm.coding.model.dynamic;

import lombok.Data;
import lombok.NoArgsConstructor;
import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * Enovia 动态属性表
 * 用于存储所有Enovia实体的动态扩展属性
 */
@Entity
@Table(name = "ENOVIA_DYNAMIC_ATTRIBUTE", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"ENTITY_TYPE", "ENTITY_ID", "ATTRIBUTE_NAME"}))
@Data
@NoArgsConstructor
public class EnoviaDynamicAttribute {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 实体类型（如：EnoviaType, EnoviaClassification等）
     */
    @Column(name = "ENTITY_TYPE", nullable = false)
    private String entityType;
    
    /**
     * 实体ID
     */
    @Column(name = "ENTITY_ID", nullable = false)
    private Long entityId;
    
    /**
     * 属性名称
     */
    @Column(name = "ATTRIBUTE_NAME", nullable = false)
    private String attributeName;
    
    /**
     * 属性显示名称
     */
    @Column(name = "DISPLAY_NAME")
    private String displayName;
    
    /**
     * 属性值类型
     */
    @Column(name = "VALUE_TYPE", nullable = false)
    @Enumerated(EnumType.STRING)
    private ValueType valueType;
    
    /**
     * 字符串值
     */
    @Column(name = "STRING_VALUE", columnDefinition = "TEXT")
    private String stringValue;
    
    /**
     * 数字值
     */
    @Column(name = "NUMBER_VALUE")
    private Double numberValue;
    
    /**
     * 布尔值
     */
    @Column(name = "BOOLEAN_VALUE")
    private Boolean booleanValue;
    
    /**
     * 日期值
     */
    @Column(name = "DATE_VALUE")
    private LocalDateTime dateValue;
    
    /**
     * JSON值（用于复杂对象）
     */
    @Column(name = "JSON_VALUE", columnDefinition = "TEXT")
    private String jsonValue;
    
    /**
     * 属性描述
     */
    @Column(name = "DESCRIPTION")
    private String description;
    
    /**
     * 是否必填
     */
    @Column(name = "IS_REQUIRED")
    private Boolean isRequired = false;
    
    /**
     * 是否多值
     */
    @Column(name = "IS_MULTIVALUE")
    private Boolean isMultivalue = false;
    
    /**
     * 排序顺序
     */
    @Column(name = "SORT_ORDER")
    private Integer sortOrder = 0;
    
    /**
     * 创建时间
     */
    @Column(name = "CREATED_AT", updatable = false)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @Column(name = "UPDATED_AT")
    private LocalDateTime updatedAt;
    
    /**
     * 同步状态
     */
    @Column(name = "SYNC_STATUS")
    @Enumerated(EnumType.STRING)
    private SyncStatus syncStatus = SyncStatus.PENDING;
    
    /**
     * 最后同步时间
     */
    @Column(name = "LAST_SYNC_TIME")
    private LocalDateTime lastSyncTime;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    /**
     * 获取实际值
     */
    public Object getValue() {
        switch (valueType) {
            case STRING:
                return stringValue;
            case NUMBER:
                return numberValue;
            case BOOLEAN:
                return booleanValue;
            case DATE:
                return dateValue;
            case JSON:
                return jsonValue;
            default:
                return null;
        }
    }
    
    /**
     * 设置值
     */
    public void setValue(Object value) {
        if (value == null) {
            clearValues();
            return;
        }
        
        if (value instanceof String) {
            this.valueType = ValueType.STRING;
            this.stringValue = (String) value;
        } else if (value instanceof Number) {
            this.valueType = ValueType.NUMBER;
            this.numberValue = ((Number) value).doubleValue();
        } else if (value instanceof Boolean) {
            this.valueType = ValueType.BOOLEAN;
            this.booleanValue = (Boolean) value;
        } else if (value instanceof LocalDateTime) {
            this.valueType = ValueType.DATE;
            this.dateValue = (LocalDateTime) value;
        } else {
            this.valueType = ValueType.JSON;
            this.jsonValue = value.toString();
        }
    }
    
    private void clearValues() {
        this.stringValue = null;
        this.numberValue = null;
        this.booleanValue = null;
        this.dateValue = null;
        this.jsonValue = null;
    }
    
    /**
     * 值类型枚举
     */
    public enum ValueType {
        STRING,     // 字符串
        NUMBER,     // 数字
        BOOLEAN,    // 布尔值
        DATE,       // 日期时间
        JSON        // JSON对象
    }
    
    /**
     * 同步状态枚举
     */
    public enum SyncStatus {
        PENDING,    // 待同步
        SYNCING,    // 同步中
        SUCCESS,    // 同步成功
        FAILED      // 同步失败
    }
}
