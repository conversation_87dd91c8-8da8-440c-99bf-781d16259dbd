package com.plm.coding;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * PLM物料编码规则引擎Spring Boot启动类。
 * 该应用提供RESTful API用于物料编码规则的配置、测试和管理。
 */
@SpringBootApplication(scanBasePackages = {"com.plm.coding"})
@EnableCaching
@EnableAsync
@EnableScheduling
public class PlmCodingApplication {

    public static void main(String[] args) {
        SpringApplication.run(PlmCodingApplication.class, args);
    }

}
