// RuleEngineService.java (New custom rule engine)
package com.plm.coding.service;

import com.plm.coding.model.*;
import com.plm.coding.repository.CodingRuleRepository;
import com.plm.coding.repository.CodingRuleSegmentRepository;
import com.plm.coding.repository.EnoviaClassificationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * Custom Rule Engine Service for generating material codes based on configured segments.
 * This service replaces the Drools-based engine.
 */
@Service
public class RuleEngineService {

    @Autowired
    private CodingRuleRepository codingRuleRepository;

    @Autowired
    private CodingRuleSegmentRepository codingRuleSegmentRepository;

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private CheckDigitService checkDigitService;

    @Autowired
    private EnoviaClassificationRepository classificationRepository;

    /**
     * Finds the most suitable coding rule for a given object type and classification,
     * then executes it to generate a material code.
     *
     * @param materialFact Input fact containing object type, classification, and attributes.
     * @return MaterialFact with generated code and segments.
     */
    @Transactional
    public MaterialFact generateCode(MaterialFact materialFact) {
        // Find the active rule with the highest priority for the given object type
        List<CodingRule> rules = codingRuleRepository.findByObjectTypeCodeAndIsActiveOrderByPriorityAsc(
                materialFact.getObjectTypeCode(), "Y");

        if (rules.isEmpty()) {
            materialFact.setGeneratedCode("NO_RULE_FOUND");
            return materialFact;
        }

        CodingRule rule = rules.get(0); // Get the first rule (highest priority due to OrderByPriorityAsc)
        // Load segments for the rule, ordered by segmentOrder
        List<CodingRuleSegment> segments = codingRuleSegmentRepository.findByRuleIdOrderBySegmentOrderAsc(rule.getId());

        StringBuilder generatedCodeBuilder = new StringBuilder();
        SimpleDateFormat sdf = new SimpleDateFormat();
        Date currentDate = new Date();

        for (CodingRuleSegment segment : segments) {
            String segmentValue = "";
            String rawValue = "";

            switch (segment.getSegmentType()) {
                case "FIXED":
                    rawValue = segment.getFixedValue();
                    break;
                case "TIME":
                    if (segment.getTimeFormat() != null) {
                        sdf.applyPattern(segment.getTimeFormat());
                        rawValue = sdf.format(currentDate);
                    } else {
                        rawValue = ""; // Or throw error
                    }
                    break;
                case "ATTRIBUTE":
                    if (materialFact.getAttributes() != null && segment.getAttributeCode() != null) {
                        Object attr = materialFact.getAttributes().get(segment.getAttributeCode());
                        rawValue = (attr != null) ? String.valueOf(attr) : "";
                    } else {
                        rawValue = "";
                    }
                    break;
                case "CLASSIFICATION":
                    // Get classification code from materialFact's classificationCode
                    // Assuming materialFact.getClassificationCode() is the direct code like "ELECTRONIC"
                    rawValue = materialFact.getClassificationCode();
                    break;
                case "CLASSIFICATION_ATTRIBUTE":
                    // This implies fetching an attribute specific to the material's classification.
                    // For this demo, we'll treat it similar to a general attribute for simplicity,
                    // but in a real system, you might fetch it from classification-specific attributes.
                    if (materialFact.getAttributes() != null && segment.getAttributeCode() != null) {
                        Object attr = materialFact.getAttributes().get(segment.getAttributeCode());
                        rawValue = (attr != null) ? String.valueOf(attr) : "";
                    } else {
                        rawValue = "";
                    }
                    break;
                case "SEQUENCE":
                    if (segment.getSequenceName() != null) {
                        rawValue = sequenceService.getNextSequence(segment.getSequenceName(), segment.getLength());
                    } else {
                        rawValue = ""; // Or throw error
                    }
                    break;
                default:
                    rawValue = ""; // Unknown segment type
            }

            // Apply padding and truncation
            segmentValue = applyPaddingAndTruncation(rawValue, segment);
            // 新增前缀后缀拼接
            String fullSegmentValue = (segment.getPrefix() == null ? "" : segment.getPrefix()) + segmentValue + (segment.getSuffix() == null ? "" : segment.getSuffix());
            materialFact.appendSegment(fullSegmentValue);
            generatedCodeBuilder.append(fullSegmentValue);
        }

        // Apply check digit if enabled
        // if ("Y".equals(rule.getEnableCheckDigit())) {
        //   String baseCode = generatedCodeBuilder.toString();
        //   String checkDigit = checkDigitService.calculate("LUHN", baseCode); // Assuming LUHN algorithm
        //   materialFact.appendSegment(checkDigit);
        //   generatedCodeBuilder.append(checkDigit);
        // }

        materialFact.setGeneratedCode(generatedCodeBuilder.toString());
        return materialFact;
    }

    private String applyPaddingAndTruncation(String value, CodingRuleSegment segment) {
        if (value == null) {
            value = "";
        }

        // Truncation first
        if (value.length() > segment.getLength()) {
            if ("LEFT".equalsIgnoreCase(segment.getTruncateDirection())) {
                value = value.substring(value.length() - segment.getLength());
            } else { // RIGHT or default
                value = value.substring(0, segment.getLength());
            }
        }

        // Padding next
        if (value.length() < segment.getLength()) {
            char padChar = segment.getPaddingChar() != null ? segment.getPaddingChar().charAt(0) : '0';
            if ("LEFT".equalsIgnoreCase(segment.getPaddingDirection())) {
                value = String.format("%" + segment.getLength() + "s", value).replace(' ', padChar);
            } else { // RIGHT or default
                value = String.format("%-" + segment.getLength() + "s", value).replace(' ', padChar);
            }
        }
        return value;
    }

    /**
     * 预览生成编码（不落库，顺序码不递增）
     */
    public MaterialFact generateCodePreview(CodingRule rule, MaterialFact materialFact) {
        // 直接用传入的 rule（测试用），segments 也从 rule 取
        List<CodingRuleSegment> segments = rule.getSegments();
        StringBuilder generatedCodeBuilder = new StringBuilder();
        SimpleDateFormat sdf = new SimpleDateFormat();
        Date currentDate = new Date();
        materialFact.getSegments().clear();
        for (CodingRuleSegment segment : segments) {
            String segmentValue = "";
            String rawValue = "";
            switch (segment.getSegmentType()) {
                case "FIXED":
                    rawValue = segment.getFixedValue();
                    break;
                case "TIME":
                    if (segment.getTimeFormat() != null) {
                        sdf.applyPattern(segment.getTimeFormat());
                        rawValue = sdf.format(currentDate);
                    } else {
                        rawValue = "";
                    }
                    break;
                case "ATTRIBUTE":
                    if (materialFact.getAttributes() != null && segment.getAttributeCode() != null) {
                        Object attr = materialFact.getAttributes().get(segment.getAttributeCode());
                        rawValue = (attr != null) ? String.valueOf(attr) : "";
                    } else {
                        rawValue = "";
                    }
                    break;
                case "CLASSIFICATION":
                    rawValue = materialFact.getClassificationCode();
                    break;
                case "CLASSIFICATION_ATTRIBUTE":
                    if (materialFact.getAttributes() != null && segment.getAttributeCode() != null) {
                        Object attr = materialFact.getAttributes().get(segment.getAttributeCode());
                        rawValue = (attr != null) ? String.valueOf(attr) : "";
                    } else {
                        rawValue = "";
                    }
                    break;
                case "SEQUENCE":
                    if (segment.getSequenceName() != null) {
                        rawValue = sequenceService.peekNextSequence(segment.getSequenceName(), segment.getLength());
                    } else {
                        rawValue = "";
                    }
                    break;
                default:
                    rawValue = "";
            }
            segmentValue = applyPaddingAndTruncation(rawValue, segment);
            // 新增前缀后缀拼接
            String fullSegmentValue = (segment.getPrefix() == null ? "" : segment.getPrefix()) + segmentValue + (segment.getSuffix() == null ? "" : segment.getSuffix());
            materialFact.appendSegment(fullSegmentValue);
            generatedCodeBuilder.append(fullSegmentValue);
        }
        // 校验位
        // if (rule.getEnableCheckDigit() != null && "Y".equals(rule.getEnableCheckDigit())) {
        //   String baseCode = generatedCodeBuilder.toString();
        //   String checkDigit = checkDigitService.calculate("LUHN", baseCode);
        //   materialFact.appendSegment(checkDigit);
        //   generatedCodeBuilder.append(checkDigit);
        // }
        materialFact.setGeneratedCode(generatedCodeBuilder.toString());
        return materialFact;
    }
}


