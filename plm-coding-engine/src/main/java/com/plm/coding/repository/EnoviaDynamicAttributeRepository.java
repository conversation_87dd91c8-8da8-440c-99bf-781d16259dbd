package com.plm.coding.repository;

import com.plm.coding.model.dynamic.EnoviaDynamicAttribute;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Enovia 动态属性 Repository
 */
@Repository
public interface EnoviaDynamicAttributeRepository extends JpaRepository<EnoviaDynamicAttribute, Long> {
    
    /**
     * 根据实体类型和实体ID查找所有动态属性
     */
    List<EnoviaDynamicAttribute> findByEntityTypeAndEntityId(String entityType, Long entityId);
    
    /**
     * 根据实体类型、实体ID和属性名称查找动态属性
     */
    Optional<EnoviaDynamicAttribute> findByEntityTypeAndEntityIdAndAttributeName(
            String entityType, Long entityId, String attributeName);
    
    /**
     * 根据实体类型查找所有动态属性
     */
    List<EnoviaDynamicAttribute> findByEntityType(String entityType);
    
    /**
     * 根据属性名称查找所有动态属性
     */
    List<EnoviaDynamicAttribute> findByAttributeName(String attributeName);
    
    /**
     * 根据值类型查找动态属性
     */
    List<EnoviaDynamicAttribute> findByValueType(EnoviaDynamicAttribute.ValueType valueType);
    
    /**
     * 根据是否必填查找动态属性
     */
    List<EnoviaDynamicAttribute> findByIsRequired(Boolean isRequired);
    
    /**
     * 根据是否多值查找动态属性
     */
    List<EnoviaDynamicAttribute> findByIsMultivalue(Boolean isMultivalue);
    
    /**
     * 根据实体类型和实体ID查找所有动态属性，按排序顺序排列
     */
    List<EnoviaDynamicAttribute> findByEntityTypeAndEntityIdOrderBySortOrder(
            String entityType, Long entityId);
    
    /**
     * 查找字符串值匹配的动态属性
     */
    List<EnoviaDynamicAttribute> findByStringValueContaining(String value);
    
    /**
     * 查找数字值在指定范围内的动态属性
     */
    @Query("SELECT d FROM EnoviaDynamicAttribute d WHERE d.valueType = 'NUMBER' AND d.numberValue BETWEEN :minValue AND :maxValue")
    List<EnoviaDynamicAttribute> findByNumberValueBetween(Double minValue, Double maxValue);
    
    /**
     * 查找布尔值匹配的动态属性
     */
    List<EnoviaDynamicAttribute> findByBooleanValue(Boolean value);
    
    /**
     * 删除指定实体的所有动态属性
     */
    void deleteByEntityTypeAndEntityId(String entityType, Long entityId);
    
    /**
     * 检查是否存在指定的动态属性
     */
    boolean existsByEntityTypeAndEntityIdAndAttributeName(
            String entityType, Long entityId, String attributeName);
    
    /**
     * 根据同步状态查找动态属性
     */
    List<EnoviaDynamicAttribute> findBySyncStatus(EnoviaDynamicAttribute.SyncStatus syncStatus);
    
    /**
     * 统计指定实体的动态属性数量
     */
    @Query("SELECT COUNT(d) FROM EnoviaDynamicAttribute d WHERE d.entityType = :entityType AND d.entityId = :entityId")
    Long countByEntityTypeAndEntityId(String entityType, Long entityId);
}
