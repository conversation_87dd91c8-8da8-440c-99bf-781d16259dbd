package com.plm.coding.repository;

import com.plm.coding.model.EnoviaTypeAttribute;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Enovia Type Attribute Repository
 */
@Repository
public interface EnoviaTypeAttributeRepository extends JpaRepository<EnoviaTypeAttribute, Long> {
    
    /**
     * 根据类型ID查找属性
     */
    List<EnoviaTypeAttribute> findByEnoviaTypeId(Long typeId);
    
    /**
     * 根据类型名称和属性名称查找
     */
    @Query("SELECT a FROM EnoviaTypeAttribute a WHERE a.enoviaType.typeName = :typeName AND a.attributeName = :attributeName")
    Optional<EnoviaTypeAttribute> findByTypeNameAndAttributeName(String typeName, String attributeName);

    /**
     * 根据类型实体和属性名称查找
     */
    Optional<EnoviaTypeAttribute> findByEnoviaTypeAndAttributeName(EnoviaType enoviaType, String attributeName);
    
    /**
     * 根据同步状态查找
     */
    List<EnoviaTypeAttribute> findBySyncStatus(EnoviaTypeAttribute.SyncStatus syncStatus);
    
    /**
     * 查找需要同步的属性（最后同步时间早于指定时间）
     */
    @Query("SELECT a FROM EnoviaTypeAttribute a WHERE a.lastSyncTime IS NULL OR a.lastSyncTime < :syncTime")
    List<EnoviaTypeAttribute> findAttributesNeedingSync(LocalDateTime syncTime);
    
    /**
     * 根据是否必填查找
     */
    List<EnoviaTypeAttribute> findByIsRequired(Boolean isRequired);
    
    /**
     * 根据是否多值查找
     */
    List<EnoviaTypeAttribute> findByIsMultivalue(Boolean isMultivalue);
    
    /**
     * 根据数据类型查找
     */
    List<EnoviaTypeAttribute> findByDataType(String dataType);
}
