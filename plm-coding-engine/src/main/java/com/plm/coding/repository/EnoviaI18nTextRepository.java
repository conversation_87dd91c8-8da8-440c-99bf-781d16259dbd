package com.plm.coding.repository;

import com.plm.coding.model.i18n.EnoviaI18nText;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Enovia 国际化文本 Repository
 */
@Repository
public interface EnoviaI18nTextRepository extends JpaRepository<EnoviaI18nText, Long> {
    
    /**
     * 根据实体类型、实体ID和语言代码查找所有国际化文本
     */
    List<EnoviaI18nText> findByEntityTypeAndEntityIdAndLanguageCode(
            String entityType, Long entityId, String languageCode);
    
    /**
     * 根据实体类型、实体ID、字段名称和语言代码查找国际化文本
     */
    Optional<EnoviaI18nText> findByEntityTypeAndEntityIdAndFieldNameAndLanguageCode(
            String entityType, Long entityId, String fieldName, String languageCode);
    
    /**
     * 根据实体类型和实体ID查找所有国际化文本
     */
    List<EnoviaI18nText> findByEntityTypeAndEntityId(String entityType, Long entityId);
    
    /**
     * 根据实体类型、实体ID和字段名称查找所有语言的文本
     */
    List<EnoviaI18nText> findByEntityTypeAndEntityIdAndFieldName(
            String entityType, Long entityId, String fieldName);
    
    /**
     * 查找默认语言的文本
     */
    List<EnoviaI18nText> findByEntityTypeAndEntityIdAndIsDefault(
            String entityType, Long entityId, Boolean isDefault);
    
    /**
     * 根据语言代码查找所有文本
     */
    List<EnoviaI18nText> findByLanguageCode(String languageCode);
    
    /**
     * 根据实体类型查找所有文本
     */
    List<EnoviaI18nText> findByEntityType(String entityType);
    
    /**
     * 查找指定实体的默认语言文本
     */
    @Query("SELECT i FROM EnoviaI18nText i WHERE i.entityType = :entityType AND i.entityId = :entityId AND i.fieldName = :fieldName AND i.isDefault = true")
    Optional<EnoviaI18nText> findDefaultText(String entityType, Long entityId, String fieldName);
    
    /**
     * 查找指定实体的首选语言文本（优先返回指定语言，否则返回默认语言）
     */
    @Query("SELECT i FROM EnoviaI18nText i WHERE i.entityType = :entityType AND i.entityId = :entityId AND i.fieldName = :fieldName AND (i.languageCode = :preferredLanguage OR i.isDefault = true) ORDER BY CASE WHEN i.languageCode = :preferredLanguage THEN 0 ELSE 1 END")
    List<EnoviaI18nText> findPreferredText(String entityType, Long entityId, String fieldName, String preferredLanguage);
    
    /**
     * 删除指定实体的所有国际化文本
     */
    void deleteByEntityTypeAndEntityId(String entityType, Long entityId);
    
    /**
     * 检查是否存在指定的国际化文本
     */
    boolean existsByEntityTypeAndEntityIdAndFieldNameAndLanguageCode(
            String entityType, Long entityId, String fieldName, String languageCode);
}
