// MaterialClassificationRepository.java (Added findByCode)
package com.plm.coding.repository;

import com.plm.coding.model.EnoviaClassification;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface EnoviaClassificationRepository extends JpaRepository<EnoviaClassification, Long> {
    @EntityGraph(value = "EnoviaClassification.withAttributes")
    List<EnoviaClassification> findAll();

    @EntityGraph(value = "EnoviaClassification.withAttributes")
    Optional<EnoviaClassification> findById(Long id);

    @EntityGraph(value = "EnoviaClassification.withAttributes")
    Optional<EnoviaClassification> findByCode(String code);
}
