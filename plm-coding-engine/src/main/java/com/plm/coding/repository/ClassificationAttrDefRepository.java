// ClassificationAttrDefRepository.java (No change)
package com.plm.coding.repository;

import com.plm.coding.model.EnvoiaClassificationAttr;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.Optional;

@Repository
public interface ClassificationAttrDefRepository extends JpaRepository<EnvoiaClassificationAttr, Long> {
    List<EnvoiaClassificationAttr> findByClassificationId(Long classificationId);

    /**
     * 根据分类ID和属性代码查找属性定义
     */
    Optional<EnvoiaClassificationAttr> findByClassificationIdAndAttributeCode(Long classificationId, String attributeCode);
}
